import React from "react";
import "../../styles/SectionWrapper.css";

/**
 * Section Wrapper Component
 * A reusable wrapper component for consistent section layout
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} props.title - Section title
 * @param {React.ReactNode} props.icon - Icon component
 * @param {React.ReactNode} props.action - Action component (buttons, etc.)
 * @param {string} props.className - Additional CSS class names
 */
const SectionWrapper = ({ children, title, icon, action, className = "" }) => {
  return (
    <div className={`SectionWrapper ${className}`}>
      {title && (
        <div className="bordrdiv mb-30">
          <div className="SectionWrapper__header">
            <h2 className="SectionWrapper__title">
              {icon}
              {title}
            </h2>
            {action && (
              <div className="SectionWrapper__action">
                {action}
              </div>
            )}
          </div>
        </div>
      )}
      <div className="SectionWrapper__content">{children}</div>
    </div>
  );
};

export default SectionWrapper;
