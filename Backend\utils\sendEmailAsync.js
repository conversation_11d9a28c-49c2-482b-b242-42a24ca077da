const sendEmail = require('./sendEmail');

/**
 * Send email asynchronously without blocking the main application flow
 * This function returns immediately and handles email sending in the background
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email
 * @param {string} options.email - Alternative recipient email field
 * @param {string} options.subject - Email subject
 * @param {string} options.message - Plain text message
 * @param {string} options.html - HTML message
 * @param {string} context - Context for logging (e.g., 'order-receipt', 'otp-verification')
 * @returns {Promise<void>} - Returns immediately, doesn't wait for email to be sent
 */
const sendEmailAsync = async (options, context = 'email') => {
  // Return immediately, don't wait for email to be sent
  setImmediate(async () => {
    try {
      await sendEmail(options);
      console.log(`✅ ${context} email sent successfully to ${options.to || options.email}`);
    } catch (error) {
      console.error(`❌ Failed to send ${context} email to ${options.to || options.email}:`, error.message);
      
      // Log additional details for debugging
      if (error.code === 'EMESSAGE' && error.response?.includes('451')) {
        console.error(`🚫 Rate limit error for ${context} email - this is expected and will be retried automatically`);
      } else {
        console.error(`💥 Unexpected error for ${context} email:`, {
          code: error.code,
          command: error.command,
          response: error.response
        });
      }
    }
  });
  
  // Return immediately
  return Promise.resolve();
};

/**
 * Send multiple emails asynchronously with staggered timing
 * @param {Array} emailList - Array of email options objects
 * @param {string} context - Context for logging
 * @param {number} staggerDelay - Delay between emails in milliseconds (default: 1000ms)
 * @returns {Promise<void>} - Returns immediately
 */
const sendMultipleEmailsAsync = async (emailList, context = 'bulk-email', staggerDelay = 1000) => {
  if (!Array.isArray(emailList) || emailList.length === 0) {
    console.log(`📧 No emails to send for ${context}`);
    return Promise.resolve();
  }

  console.log(`📧 Scheduling ${emailList.length} ${context} emails to be sent asynchronously`);

  // Schedule emails with staggered timing to avoid rate limits
  emailList.forEach((emailOptions, index) => {
    const delay = index * staggerDelay;
    setTimeout(() => {
      sendEmailAsync(emailOptions, `${context}-${index + 1}`);
    }, delay);
  });

  return Promise.resolve();
};

/**
 * Send email with retry logic for critical emails
 * This function will attempt to send the email multiple times if it fails
 * @param {Object} options - Email options
 * @param {string} context - Context for logging
 * @param {number} maxRetries - Maximum number of retry attempts (default: 3)
 * @param {number} retryDelay - Delay between retries in milliseconds (default: 5000ms)
 * @returns {Promise<boolean>} - Returns true if email was sent successfully, false otherwise
 */
const sendCriticalEmailAsync = async (options, context = 'critical-email', maxRetries = 3, retryDelay = 5000) => {
  let attempts = 0;
  
  const attemptSend = async () => {
    attempts++;
    try {
      await sendEmail(options);
      console.log(`✅ ${context} email sent successfully on attempt ${attempts}`);
      return true;
    } catch (error) {
      console.error(`❌ ${context} email failed on attempt ${attempts}:`, error.message);
      
      if (attempts < maxRetries) {
        const delay = retryDelay * attempts; // Increasing delay
        console.log(`🔄 Retrying ${context} email in ${delay}ms (attempt ${attempts + 1}/${maxRetries})`);
        setTimeout(attemptSend, delay);
      } else {
        console.error(`💥 ${context} email failed permanently after ${maxRetries} attempts`);
        return false;
      }
    }
  };

  // Start the first attempt asynchronously
  setImmediate(attemptSend);
  
  // Return immediately
  return Promise.resolve(true);
};

module.exports = {
  sendEmailAsync,
  sendMultipleEmailsAsync,
  sendCriticalEmailAsync
};
