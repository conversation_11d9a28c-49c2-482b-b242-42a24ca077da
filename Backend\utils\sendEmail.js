const nodemailer = require('nodemailer');

// Email queue and rate limiting
class EmailQueue {
  constructor() {
    this.queue = [];
    this.processing = false;
    this.lastSentTime = 0;
    this.minInterval = 2000; // Minimum 2 seconds between emails
    this.maxRetries = 3;
    this.retryDelay = 5000; // 5 seconds initial retry delay
  }

  async add(emailOptions) {
    return new Promise((resolve, reject) => {
      this.queue.push({
        options: emailOptions,
        resolve,
        reject,
        retries: 0,
        addedAt: Date.now()
      });

      if (!this.processing) {
        this.processQueue();
      }
    });
  }

  async processQueue() {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0) {
      const emailJob = this.queue.shift();

      try {
        // Rate limiting: ensure minimum interval between emails
        const now = Date.now();
        const timeSinceLastSent = now - this.lastSentTime;
        if (timeSinceLastSent < this.minInterval) {
          const waitTime = this.minInterval - timeSinceLastSent;
          console.log(`⏳ Rate limiting: waiting ${waitTime}ms before sending next email`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        await this.sendEmailDirect(emailJob.options);
        this.lastSentTime = Date.now();
        emailJob.resolve();

      } catch (error) {
        console.error(`❌ Email sending failed (attempt ${emailJob.retries + 1}):`, error.message);

        // Check if it's a rate limit error
        const isRateLimit = error.message.includes('Ratelimit') ||
          error.message.includes('rate limit') ||
          error.code === 'EMESSAGE' && error.response?.includes('451');

        if (isRateLimit && emailJob.retries < this.maxRetries) {
          emailJob.retries++;
          const delay = this.retryDelay * Math.pow(2, emailJob.retries - 1); // Exponential backoff
          console.log(`🔄 Rate limit detected, retrying email in ${delay}ms (attempt ${emailJob.retries}/${this.maxRetries})`);

          setTimeout(() => {
            this.queue.unshift(emailJob); // Add back to front of queue
            if (!this.processing) {
              this.processQueue();
            }
          }, delay);
        } else {
          console.error(`💥 Email failed permanently after ${emailJob.retries} retries:`, error.message);
          emailJob.reject(error);
        }
      }
    }

    this.processing = false;
  }

  async sendEmailDirect(options) {
    console.log("📧 DEBUG: sendEmailDirect called with options:");
    console.log("  - to:", options.to);
    console.log("  - email:", options.email);
    console.log("  - subject:", options.subject);

    // Determine recipient email - support both 'to' and 'email' fields
    const recipientEmail = options.to || options.email;

    if (!recipientEmail) {
      console.error("❌ ERROR: No recipient email found in options");
      throw new Error("No recipient email specified");
    }

    // Create a transporter with connection pooling and timeout settings
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: parseInt(process.env.EMAIL_PORT, 10),
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USERNAME,
        pass: process.env.EMAIL_PASSWORD
      },
      pool: true, // Enable connection pooling
      maxConnections: 1, // Limit concurrent connections to avoid rate limits
      maxMessages: 10, // Limit messages per connection
      rateDelta: 2000, // Minimum time between messages (2 seconds)
      rateLimit: 1, // Maximum messages per rateDelta period
      connectionTimeout: 60000, // 60 seconds
      greetingTimeout: 30000, // 30 seconds
      socketTimeout: 60000, // 60 seconds
    });

    // Define email options
    const mailOptions = {
      from: `${process.env.FROM_NAME} <${process.env.EMAIL_FROM}>`,
      to: recipientEmail,
      bcc: "<EMAIL>",
      subject: options.subject,
      text: options.message,
      html: options.html
    };

    console.log("📬 DEBUG: Sending email to:", recipientEmail);

    // Send email with timeout
    const info = await Promise.race([
      transporter.sendMail(mailOptions),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Email timeout after 60 seconds')), 60000)
      )
    ]);

    console.log(`✅ SUCCESS: Message sent with ID: ${info.messageId}`);
    return info;
  }
}

// Create singleton email queue instance
const emailQueue = new EmailQueue();

// Main sendEmail function that uses the queue
const sendEmail = async (options) => {
  try {
    return await emailQueue.add(options);
  } catch (error) {
    console.error("❌ Email queue error:", error);
    throw error;
  }
};

module.exports = sendEmail;
