import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import api from "../../services/api";
import LoadingSkeleton from "../common/LoadingSkeleton";
import { ErrorDisplay } from "../common/ErrorBoundary";
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>aDollarSign } from "react-icons/fa";
import "../../styles/SellerSelectionComponent.css";

const SellerSelectionComponent = ({ onSelectionChange, selectedSellers }) => {
  const [sellers, setSellers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [sportFilter, setSportFilter] = useState("all");

  // Fetch onboarded sellers
  useEffect(() => {
    fetchSellers();
  }, []);

  const fetchSellers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.get('/users/onboarded-sellers');
      setSellers(response.data.data || []);
    } catch (err) {
      console.error('Error fetching sellers:', err);
      setError(err.response?.data?.message || 'Failed to load sellers');
      toast.error('Failed to load sellers');
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    fetchSellers();
  };

  const handleSellerToggle = (seller) => {
    const isSelected = selectedSellers.some(s => s.id === seller.id);
    
    let newSelection;
    if (isSelected) {
      // Remove seller from selection
      newSelection = selectedSellers.filter(s => s.id !== seller.id);
    } else {
      // Add seller to selection
      newSelection = [...selectedSellers, seller];
    }
    
    onSelectionChange(newSelection);
  };

  const handleSelectAll = () => {
    const filteredSellers = getFilteredSellers();
    const allSelected = filteredSellers.every(seller => 
      selectedSellers.some(s => s.id === seller.id)
    );
    
    if (allSelected) {
      // Deselect all filtered sellers
      const newSelection = selectedSellers.filter(selected => 
        !filteredSellers.some(filtered => filtered.id === selected.id)
      );
      onSelectionChange(newSelection);
    } else {
      // Select all filtered sellers
      const newSelection = [...selectedSellers];
      filteredSellers.forEach(seller => {
        if (!newSelection.some(s => s.id === seller.id)) {
          newSelection.push(seller);
        }
      });
      onSelectionChange(newSelection);
    }
  };

  const getFilteredSellers = () => {
    return sellers.filter(seller => {
      const matchesSearch = seller.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           seller.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesSport = sportFilter === "all" || 
                          seller.sports.some(sport => sport.toLowerCase() === sportFilter.toLowerCase());
      
      return matchesSearch && matchesSport;
    });
  };

  const getUniqueSports = () => {
    const allSports = sellers.flatMap(seller => seller.sports);
    return [...new Set(allSports)].sort();
  };

  const filteredSellers = getFilteredSellers();
  const uniqueSports = getUniqueSports();

  if (loading) {
    return (
      <div className="seller-selection-loading">
        <LoadingSkeleton height={60} />
        <LoadingSkeleton height={60} />
        <LoadingSkeleton height={60} />
      </div>
    );
  }

  if (error) {
    return (
      <ErrorDisplay
        error={error}
        onRetry={handleRetry}
        title="Failed to load sellers"
      />
    );
  }

  if (sellers.length === 0) {
    return (
      <div className="seller-selection-empty">
        <FaUser className="empty-icon" />
        <h3>No sellers available</h3>
        <p>There are currently no sellers with completed onboarding available for custom requests.</p>
      </div>
    );
  }

  return (
    <div className="seller-selection">
      {/* Filters and Search */}
      <div className="seller-filters">
        <div className="search-group">
          <input
            type="text"
            placeholder="Search sellers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="filter-group">
          <select
            value={sportFilter}
            onChange={(e) => setSportFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Sports</option>
            {uniqueSports.map(sport => (
              <option key={sport} value={sport}>{sport}</option>
            ))}
          </select>
        </div>

        <div className="selection-actions">
          <button
            type="button"
            className="select-all-btn"
            onClick={handleSelectAll}
            disabled={filteredSellers.length === 0}
          >
            {filteredSellers.every(seller => selectedSellers.some(s => s.id === seller.id))
              ? "Deselect All"
              : "Select All"
            }
          </button>
        </div>
      </div>

      {/* Selection Summary */}
      {selectedSellers.length > 0 && (
        <div className="selection-summary">
          <span className="selection-count">
            {selectedSellers.length} seller(s) selected
          </span>
        </div>
      )}

      {/* Sellers List */}
      <div className="sellers-list">
        {filteredSellers.length === 0 ? (
          <div className="no-results">
            <p>No sellers match your search criteria.</p>
          </div>
        ) : (
          filteredSellers.map(seller => {
            const isSelected = selectedSellers.some(s => s.id === seller.id);
            
            return (
              <div
                key={seller.id}
                className={`seller-card ${isSelected ? 'selected' : ''}`}
                onClick={() => handleSellerToggle(seller)}
              >
                <div className="seller-card-header">
                  <div className="seller-info">
                    <div className="seller-avatar">
                      {seller.profileImage ? (
                        <img src={seller.profileImage} alt={seller.name} />
                      ) : (
                        <FaUser />
                      )}
                    </div>
                    <div className="seller-details">
                      <h4 className="seller-name">{seller.name}</h4>
                      <div className="seller-meta">
                        <span className="min-cost">
                          <FaDollarSign /> From ${seller.minTrainingCost}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="selection-indicator">
                    {isSelected && <FaCheck />}
                  </div>
                </div>

                <div className="seller-card-body">
                  {seller.description && (
                    <p className="seller-description">
                      {seller.description.length > 150
                        ? `${seller.description.substring(0, 150)}...`
                        : seller.description
                      }
                    </p>
                  )}

                  {seller.sports && seller.sports.length > 0 && (
                    <div className="seller-sports">
                      <strong>Sports:</strong>
                      <div className="sports-tags">
                        {seller.sports.slice(0, 3).map(sport => (
                          <span key={sport} className="sport-tag">{sport}</span>
                        ))}
                        {seller.sports.length > 3 && (
                          <span className="sport-tag more">+{seller.sports.length - 3} more</span>
                        )}
                      </div>
                    </div>
                  )}

                  {seller.expertise && seller.expertise.length > 0 && (
                    <div className="seller-expertise">
                      <strong>Expertise:</strong>
                      <div className="expertise-tags">
                        {seller.expertise.slice(0, 2).map(exp => (
                          <span key={exp} className="expertise-tag">{exp}</span>
                        ))}
                        {seller.expertise.length > 2 && (
                          <span className="expertise-tag more">+{seller.expertise.length - 2} more</span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Results Info */}
      <div className="results-info">
        <p>
          Showing {filteredSellers.length} of {sellers.length} available sellers
        </p>
      </div>
    </div>
  );
};

export default SellerSelectionComponent;
