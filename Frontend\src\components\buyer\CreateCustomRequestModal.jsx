import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import api from "../../services/api";
import useModalScrollLock from "../../hooks/useModalScrollLock";
import SellerSelectionComponent from "./SellerSelectionComponent";
import { FaTimes, FaArrowLeft, FaArrowRight } from "react-icons/fa";
import "../../styles/CreateCustomRequestModal.css";

const CreateCustomRequestModal = ({ isOpen, onClose, onRequestCreated }) => {
  // Use modal scroll lock hook
  useModalScrollLock(isOpen);

  const { user } = useSelector((state) => state.auth);

  // Multi-step form state
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedSellers, setSelectedSellers] = useState([]);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    sport: "Basketball",
    contentType: "Video",
    requestedDeliveryDate: "",
    budget: ""
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [showSuccessState, setShowSuccessState] = useState(false);

  // Sports options
  const sportsOptions = [
    'Basketball', 'Football', 'Soccer', 'Baseball', 'Tennis', 
    'Golf', 'Swimming', 'Volleyball', 'Running', 'Cycling', 
    'Fitness', 'Yoga', 'Other'
  ];

  // Content type options
  const contentTypeOptions = [
    'Video', 'Document', 'Audio', 'Image', 'Text'
  ];

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(1);
      setSelectedSellers([]);
      setFormData({
        title: "",
        description: "",
        sport: "Basketball",
        contentType: "Video",
        requestedDeliveryDate: "",
        budget: ""
      });
      setErrors({});
      setShowSuccessState(false);
      setIsSubmitting(false);
    }
  }, [isOpen]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateStep1 = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    } else if (formData.title.length > 100) {
      newErrors.title = "Title cannot exceed 100 characters";
    }
    
    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    } else if (formData.description.length > 2000) {
      newErrors.description = "Description cannot exceed 2000 characters";
    }
    
    if (!formData.budget || parseFloat(formData.budget) <= 0) {
      newErrors.budget = "Please enter a valid budget amount";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = () => {
    if (selectedSellers.length === 0) {
      toast.error("Please select at least one seller");
      return false;
    }
    return true;
  };

  const handleNextStep = () => {
    if (currentStep === 1 && validateStep1()) {
      setCurrentStep(2);
    }
  };

  const handlePrevStep = () => {
    if (currentStep === 2) {
      setCurrentStep(1);
    }
  };

  const handleSellerSelection = (sellers) => {
    setSelectedSellers(sellers);
  };

  const handleSubmit = async () => {
    if (!validateStep2()) return;

    setIsSubmitting(true);
    
    try {
      // Submit requests to each selected seller
      const requestPromises = selectedSellers.map(seller => {
        const requestData = {
          sellerId: seller.id,
          title: formData.title,
          description: formData.description,
          sport: formData.sport,
          contentType: formData.contentType,
          requestedDeliveryDate: formData.requestedDeliveryDate || null,
          budget: parseFloat(formData.budget)
        };
        
        return api.post('/requests', requestData);
      });

      await Promise.all(requestPromises);

      setShowSuccessState(true);
      toast.success(`Custom request sent to ${selectedSellers.length} seller(s) successfully!`);
      
      // Call the callback after a short delay to show success state
      setTimeout(() => {
        onRequestCreated();
      }, 2000);
      
    } catch (error) {
      console.error('Error submitting custom requests:', error);
      toast.error(error.response?.data?.message || "Failed to submit custom requests");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCloseModal = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget && !isSubmitting) {
      handleCloseModal();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="create-request-modal-overlay" onClick={handleOverlayClick}>
      <div className="create-request-modal">
        <div className="create-request-modal__header">
          <h2>
            {showSuccessState ? "Request Sent!" : "Create Custom Request"}
          </h2>
          {!isSubmitting && (
            <button
              className="close-btn"
              onClick={handleCloseModal}
              aria-label="Close modal"
            >
              <FaTimes />
            </button>
          )}
        </div>

        <div className="create-request-modal__content">
          {showSuccessState ? (
            <div className="success-state">
              <div className="success-icon">✅</div>
              <h3>Your custom request has been sent!</h3>
              <p>
                Your request has been sent to {selectedSellers.length} seller(s). 
                They will review your request and respond soon.
              </p>
              <p className="success-note">
                You can track the status of your requests in this dashboard.
              </p>
            </div>
          ) : (
            <>
              {/* Progress Steps */}
              <div className="progress-steps">
                <div className={`step ${currentStep >= 1 ? 'active' : ''}`}>
                  <span className="step-number">1</span>
                  <span className="step-label">Request Details</span>
                </div>
                <div className="step-connector"></div>
                <div className={`step ${currentStep >= 2 ? 'active' : ''}`}>
                  <span className="step-number">2</span>
                  <span className="step-label">Select Sellers</span>
                </div>
              </div>

              {/* Step Content */}
              {currentStep === 1 ? (
                <div className="step-content">
                  <h3>Tell us about your custom request</h3>
                  
                  <div className="form-group">
                    <label htmlFor="title">Request Title *</label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      className={`form-input ${errors.title ? 'error' : ''}`}
                      placeholder="e.g., Custom Basketball Training Video"
                      maxLength={100}
                    />
                    {errors.title && <span className="error-message">{errors.title}</span>}
                  </div>

                  <div className="form-group">
                    <label htmlFor="description">Description *</label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      className={`form-textarea ${errors.description ? 'error' : ''}`}
                      placeholder="Describe what you're looking for in detail..."
                      rows={4}
                      maxLength={2000}
                    />
                    <div className="char-count">
                      {formData.description.length}/2000
                    </div>
                    {errors.description && <span className="error-message">{errors.description}</span>}
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="sport">Sport *</label>
                      <select
                        id="sport"
                        name="sport"
                        value={formData.sport}
                        onChange={handleInputChange}
                        className="form-select"
                      >
                        {sportsOptions.map(sport => (
                          <option key={sport} value={sport}>{sport}</option>
                        ))}
                      </select>
                    </div>

                    <div className="form-group">
                      <label htmlFor="contentType">Content Type *</label>
                      <select
                        id="contentType"
                        name="contentType"
                        value={formData.contentType}
                        onChange={handleInputChange}
                        className="form-select"
                      >
                        {contentTypeOptions.map(type => (
                          <option key={type} value={type}>{type}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="budget">Budget (USD) *</label>
                      <input
                        type="number"
                        id="budget"
                        name="budget"
                        value={formData.budget}
                        onChange={handleInputChange}
                        className={`form-input ${errors.budget ? 'error' : ''}`}
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                      />
                      {errors.budget && <span className="error-message">{errors.budget}</span>}
                    </div>

                    <div className="form-group">
                      <label htmlFor="requestedDeliveryDate">Preferred Delivery Date</label>
                      <input
                        type="date"
                        id="requestedDeliveryDate"
                        name="requestedDeliveryDate"
                        value={formData.requestedDeliveryDate}
                        onChange={handleInputChange}
                        className="form-input"
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="step-content">
                  <h3>Select Sellers</h3>
                  <p>Choose which sellers you'd like to send your custom request to:</p>
                  
                  <SellerSelectionComponent
                    onSelectionChange={handleSellerSelection}
                    selectedSellers={selectedSellers}
                  />
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="modal-actions">
                {currentStep === 2 && (
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={handlePrevStep}
                    disabled={isSubmitting}
                  >
                    <FaArrowLeft /> Back
                  </button>
                )}
                
                {currentStep === 1 ? (
                  <button
                    type="button"
                    className="btn-primary"
                    onClick={handleNextStep}
                  >
                    Next <FaArrowRight />
                  </button>
                ) : (
                  <button
                    type="button"
                    className="btn-primary"
                    onClick={handleSubmit}
                    disabled={isSubmitting || selectedSellers.length === 0}
                  >
                    {isSubmitting ? "Sending..." : `Send Request to ${selectedSellers.length} Seller(s)`}
                  </button>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateCustomRequestModal;
