/* Content Submission Modal Styles */
.content-submission-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
}

.content-submission-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.content-submission-modal__header {
  position: relative;
  padding: 20px 20px 0 20px;
}

.content-submission-modal__close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 18px;
  color: #6c757d;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.content-submission-modal__close:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.content-submission-modal__content {
  padding: 20px 40px 40px 40px;
  text-align: center;
}

.content-submission-modal__icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.content-submission-modal__icon img {
  width: 64px;
  height: 64px;
  animation: zoomInOut 2s ease-in-out infinite;
}

@keyframes zoomInOut {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

.content-submission-modal__title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  line-height: 1.3;
}

.content-submission-modal__message {
  margin-bottom: 30px;
}

.content-submission-modal__message p {
  font-size: 16px;
  color: #495057;
  line-height: 1.6;
  margin-bottom: 15px;
}

.content-submission-modal__message p:last-child {
  margin-bottom: 0;
}

.content-submission-modal__message strong {
  color: #2c3e50;
  font-weight: 600;
}

.content-submission-modal__actions {
  display: flex;
  justify-content: center;
}

.content-submission-modal__actions .btn-primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.content-submission-modal__actions .btn-primary:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.content-submission-modal__actions .btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .content-submission-modal-overlay {
    padding: 15px;
  }

  .content-submission-modal {
    width: 95%;
    max-height: 95vh;
    margin: 0;
  }

  .content-submission-modal__content {
    padding: 20px 25px 30px 25px;
  }

  .content-submission-modal__icon img {
    width: 50px;
    height: 50px;
  }

  .content-submission-modal__title {
    font-size: 20px;
  }

  .content-submission-modal__message p {
    font-size: 14px;
  }

  .content-submission-modal__actions .btn-primary {
    padding: 10px 25px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .content-submission-modal__content {
    padding: 15px 20px 25px 20px;
  }

  .content-submission-modal__title {
    font-size: 18px;
  }

  .content-submission-modal__message p {
    font-size: 13px;
  }
}
