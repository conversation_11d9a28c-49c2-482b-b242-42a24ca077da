import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectUsers,
  selectSelectedUsers,
  selectUI,
  selectUserFilters,
  selectUsersPagination,
  setSelectedUsers,
  setUserFilters,
  showUserDetailModal,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchUsers,
  updateUser,
  deleteUser,
  bulkUpdateUsers,
  bulkDeleteUsers,
} from "../../redux/slices/adminDashboardThunks";
import { showSuccess, showError } from "../../utils/toast";
import AdminLayout from "../../components/admin/AdminLayout";
import UserDetailModal from "../../components/admin/UserDetailModal";
import ConfirmationModal from "../../components/common/ConfirmationModal";
import Table from "../../components/common/Table";
import AdminTableActions from "../../components/admin/AdminTableActions";
import AdminPagination from "../../components/admin/AdminPagination";
import "../../styles/AdminUserManagement.css";
import { IMAGE_BASE_URL } from "../../utils/constants";

// Icons
import {
  FaUsers,
  FaUserTie,
  FaSearch,
  FaFilter,
  FaEye,
  FaEdit,
  FaTrash,
  FaToggleOn,
  FaToggleOff,
} from "react-icons/fa";

const AdminUserManagement = () => {
  const dispatch = useDispatch();
  const users = useSelector(selectUsers);
  const selectedUsers = useSelector(selectSelectedUsers);
  const filters = useSelector(selectUserFilters);
  const pagination = useSelector(selectUsersPagination);
  const ui = useSelector(selectUI);
  const [searchTerm, setSearchTerm] = useState(filters.search || "");
  const [roleFilter, setRoleFilter] = useState(filters.role || "all");
  const [statusFilter, setStatusFilter] = useState(filters.status || "all");

  // Confirmation modal state
  const [confirmModal, setConfirmModal] = useState({
    isOpen: false,
    type: "single", // 'single' or 'bulk'
    user: null,
    userIds: [],
    action: null,
  });
  const [isDeleting, setIsDeleting] = useState(false);

  // Initial fetch on component mount
  useEffect(() => {
    dispatch(fetchUsers({
      page: 1,
      limit: 10,
      search: "",
      role: "",
      status: "",
      sortBy: "createdAt",
      sortOrder: "desc",
    }));
  }, [dispatch]);

  // Consolidated effect for search, filter, and pagination changes
  useEffect(() => {
    const timer = setTimeout(() => {
      const currentFilters = {
        search: searchTerm,
        role: roleFilter === "all" ? "" : roleFilter,
        status: statusFilter === "all" ? "" : statusFilter,
      };

      // Update filters in Redux
      dispatch(setUserFilters(currentFilters));

      // Fetch users with current page and filters
      dispatch(fetchUsers({
        page: pagination.current,
        limit: pagination.limit,
        ...currentFilters,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
      }));
    }, 500);

    return () => clearTimeout(timer);
  }, [dispatch, searchTerm, roleFilter, statusFilter, pagination.current, pagination.limit, filters.sortBy, filters.sortOrder]);

  // Handle page change
  const handlePageChange = (newPage) => {
    dispatch(setUserFilters({ page: newPage }));
  };

  // Use users data directly since filtering is handled on backend
  const displayUsers = users.data || [];

  // Handle select all
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      dispatch(setSelectedUsers(displayUsers.map((user) => user.id)));
    } else {
      dispatch(setSelectedUsers([]));
    }
  };

  // Handle individual select
  const handleSelectUser = (userId) => {
    const newSelection = selectedUsers.includes(userId)
      ? selectedUsers.filter((id) => id !== userId)
      : [...selectedUsers, userId];
    dispatch(setSelectedUsers(newSelection));
  };

  // Handle user actions
  const handleUserAction = (user, action) => {
    switch (action) {
      case "view":
        dispatch(showUserDetailModal(user));
        break;
      case "delete":
        handleDeleteUser(user);
        break;
      case "toggle":
        handleToggleUserStatus(user);
        break;
      default:
        break;
    }
  };

  // Handle delete user
  const handleDeleteUser = (user) => {
    setConfirmModal({
      isOpen: true,
      type: "single",
      user: user,
      userIds: [],
      action: "delete",
    });
  };

  // Execute single user deletion
  const executeDeleteUser = async (user) => {
    try {
      setIsDeleting(true);
      await dispatch(deleteUser(user.id || user._id)).unwrap();
      dispatch(
        addActivity({
          id: Date.now(),
          type: "user_deletion",
          description: `User deleted: ${user.firstName} ${user.lastName}`,
          timestamp: new Date().toISOString(),
          user: "Admin",
        })
      );

      // Refresh the users list
      dispatch(fetchUsers());

      showSuccess(
        `User "${user.firstName} ${user.lastName}" has been deleted successfully!`
      );
      setConfirmModal({
        isOpen: false,
        type: "single",
        user: null,
        userIds: [],
        action: null,
      });
    } catch (error) {
      console.error("Failed to delete user:", error);
      showError(
        `Failed to delete user: ${error.message || "Please try again."}`
      );
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle toggle user status
  const handleToggleUserStatus = async (user) => {
    const newStatusNum = user.status === 1 ? 0 : 1; // Toggle between active (1) and inactive (0)
    const actionText = newStatusNum === 1 ? "activate" : "deactivate";

    if (!window.confirm(`Are you sure you want to ${actionText} this user?`)) {
      return;
    }

    try {
      await dispatch(
        updateUser({
          id: user.id || user._id,
          userData: { status: newStatusNum },
        })
      ).unwrap();
      dispatch(
        addActivity({
          id: Date.now(),
          type: "user_status_change",
          description: `User ${newStatusNum === 1 ? "activated" : "deactivated"
            }: ${user.firstName} ${user.lastName}`,
          timestamp: new Date().toISOString(),
          user: "Admin",
        })
      );

      // Refresh the users list
      dispatch(fetchUsers());

      showSuccess(`User has been ${actionText}d successfully!`);
    } catch (error) {
      console.error("Failed to update user status:", error);
      showError(
        `Failed to ${actionText} user: ${error.message || "Please try again."}`
      );
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action) => {
    if (selectedUsers.length === 0) {
      alert("Please select users first");
      return;
    }

    switch (action) {
      case "activate":
        if (
          window.confirm(`Activate ${selectedUsers.length} selected users?`)
        ) {
          try {
            await dispatch(
              bulkUpdateUsers({ userIds: selectedUsers, action: "activate" })
            ).unwrap();
            dispatch(
              addActivity({
                id: Date.now(),
                type: "bulk_user_activation",
                description: `Bulk activated ${selectedUsers.length} users`,
                timestamp: new Date().toISOString(),
                user: "Admin",
              })
            );
            dispatch(setSelectedUsers([]));

            // Refresh the users list
            dispatch(fetchUsers());

            showSuccess(
              `${selectedUsers.length} users have been activated successfully!`
            );
          } catch (error) {
            console.error("Failed to activate users:", error);
            showError(
              `Failed to activate users: ${error.message || "Please try again."
              }`
            );
          }
        }
        break;
      case "deactivate":
        if (
          window.confirm(`Deactivate ${selectedUsers.length} selected users?`)
        ) {
          try {
            await dispatch(
              bulkUpdateUsers({ userIds: selectedUsers, action: "deactivate" })
            ).unwrap();
            dispatch(
              addActivity({
                id: Date.now(),
                type: "bulk_user_deactivation",
                description: `Bulk deactivated ${selectedUsers.length} users`,
                timestamp: new Date().toISOString(),
                user: "Admin",
              })
            );
            dispatch(setSelectedUsers([]));

            // Refresh the users list
            dispatch(fetchUsers());

            showSuccess(
              `${selectedUsers.length} users have been deactivated successfully!`
            );
          } catch (error) {
            console.error("Failed to deactivate users:", error);
            showError(
              `Failed to deactivate users: ${error.message || "Please try again."
              }`
            );
          }
        }
        break;
      case "delete":
        setConfirmModal({
          isOpen: true,
          type: "bulk",
          user: null,
          userIds: selectedUsers,
          action: "delete",
        });
        break;
      default:
        break;
    }
  };

  // Execute bulk user deletion
  const executeBulkDeleteUsers = async (userIds) => {
    try {
      setIsDeleting(true);
      await dispatch(bulkDeleteUsers(userIds)).unwrap();
      dispatch(
        addActivity({
          id: Date.now(),
          type: "bulk_user_deletion",
          description: `Bulk deleted ${userIds.length} users`,
          timestamp: new Date().toISOString(),
          user: "Admin",
        })
      );
      dispatch(setSelectedUsers([]));

      // Refresh the users list
      dispatch(fetchUsers());

      showSuccess(`${userIds.length} users have been deleted successfully!`);
      setConfirmModal({
        isOpen: false,
        type: "bulk",
        user: null,
        userIds: [],
        action: null,
      });
    } catch (error) {
      console.error("Failed to delete users:", error);
      showError(
        `Failed to delete users: ${error.message || "Please try again."}`
      );
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle confirmation modal actions
  const handleConfirmAction = () => {
    if (confirmModal.type === "single" && confirmModal.user) {
      executeDeleteUser(confirmModal.user);
    } else if (
      confirmModal.type === "bulk" &&
      confirmModal.userIds.length > 0
    ) {
      executeBulkDeleteUsers(confirmModal.userIds);
    }
  };

  const handleCancelAction = () => {
    setConfirmModal({
      isOpen: false,
      type: "single",
      user: null,
      userIds: [],
      action: null,
    });
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Get status badge class and text
  const getStatusBadge = (status) => {
    switch (status) {
      case 1:
        return "status-badge active";
      case 0:
        return "status-badge inactive";
      case -1:
        return "status-badge deleted";
      default:
        return "status-badge inactive";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 1:
        return "Active";
      case 0:
        return "Inactive";
      case -1:
        return "Deleted";
      default:
        return "Unknown";
    }
  };

  // Get role badge class
  const getRoleBadge = (role) => {
    switch (role) {
      case "buyer":
        return "role-badge buyer";
      case "seller":
        return "role-badge seller";
      case "admin":
        return "role-badge admin";
      default:
        return "role-badge";
    }
  };

  // Table configuration
  const tableColumns = [
    {
      key: "select",
      label: (
        <input
          type="checkbox"
          onChange={handleSelectAll}
          checked={
            selectedUsers.length === displayUsers.length &&
            displayUsers.length > 0
          }
        />
      ),
      render: (user) => (
        <input
          type="checkbox"
          checked={selectedUsers.includes(user?._id || user?.id)}
          onChange={() => handleSelectUser(user?._id || user?.id)}
        />
      ),
      className: "select-column",
    },
    {
      key: "user",
      label: "User",
      render: (user) => (
        <div className="user-info">
          <div className="user-avatar">
            {user?.profileImage ? (
              <img
                src={IMAGE_BASE_URL + user.profileImage}
                alt={user?.firstName || "User"}
              />
            ) : ((user?.role === 'admin' ? user?.role : user?.activeRole) || "buyer") === "seller" ? (
              <FaUserTie />
            ) : (
              <FaUsers />
            )}
          </div>
          <div className="user-details">
            <span className="user-name">
              {user?.firstName || "Unknown"} {user?.lastName || "User"}
            </span>
          </div>
        </div>
      ),
    },
    {
      key: "email",
      label: "Email",
      render: (user) => user?.email || "No email",
    },
    {
      key: "role",
      label: "Role",
      render: (user) => {
        const effectiveRole = user?.role === 'admin' ? user?.role : user?.activeRole;
        return (
          <span className={getRoleBadge(effectiveRole)}>
            {effectiveRole || "buyer"}
          </span>
        );
      },
    },
    {
      key: "status",
      label: "Status",
      render: (user) => (
        <div className="status-toggle">
          <span className={getStatusBadge(user?.status)}>
            {getStatusText(user?.status)}
          </span>
          <button
            className="toggle-btn"
            onClick={() => handleUserAction(user, "toggle")}
            title={`${user?.status === 1 ? "Deactivate" : "Activate"} user`}
            disabled={user?.status === -1}
          >
            {user?.status === 1 ? (
              <FaToggleOn className="toggle-on" />
            ) : (
              <FaToggleOff className="toggle-off" />
            )}
          </button>
        </div>
      ),
    },
    {
      key: "dateJoined",
      label: "Join Date",
      render: (user) => formatDate(user?.dateJoined || new Date()),
    },
    {
      key: "actions",
      label: "Actions",
      render: (user) => (
        <AdminTableActions
          item={user}
          onView={() => handleUserAction(user, "view")}
          onDelete={() => handleUserAction(user, "delete")}
          permissions={{
            view: true,
            edit: false,
            delete: true
          }}
          tooltips={{
            view: "View User",
            delete: "Delete User",
          }}
        />
      ),
      className: "actions-column",
    },
  ];

  return (
    <AdminLayout>
      <div className="AdminUserManagement">
        <div className="AdminUserManagement__main">
          {/* Header Actions */}
          <div className="AdminUserManagement__header">
            <div className="header-left">
              <div className="search-container">
                <FaSearch className="search-icon" />
                <input
                  type="text"
                  placeholder="Search users by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="AdminUserManagement__filters">
            <div className="filter-group">

              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Roles</option>
                <option value="buyer">Buyers</option>
                <option value="seller">Sellers</option>
                <option value="admin">Admins</option>
              </select>
            </div>

            <div className="filter-group">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            {selectedUsers.length > 0 && (
              <div className="bulk-actions">
                <span className="selected-count">
                  {selectedUsers.length} selected
                </span>
                <button
                  className="btn btn-outline"
                  onClick={() => handleBulkAction("activate")}
                >
                  Activate
                </button>
                <button
                  className="btn btn-outline"
                  onClick={() => handleBulkAction("deactivate")}
                >
                  Deactivate
                </button>
                <button
                  className="btn btn-danger"
                  onClick={() => handleBulkAction("delete")}
                >
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Users Table */}
        <div className="AdminUserManagement__table">
          <Table
            columns={tableColumns}
            data={displayUsers}
            isAdmin={true}
            loading={{
              isLoading: ui.loading?.users,
              message: "Loading users...",
            }}
            emptyMessage={
              <div className="no-results">
                <FaUsers className="no-results-icon" />
                <h3>No users found</h3>
                <p>Try adjusting your search or filter criteria</p>
              </div>
            }
            className="users-table"
          />
        </div>

        {/* Pagination */}
        <AdminPagination
          currentPage={pagination.current}
          totalPages={pagination.pages}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          onPageChange={handlePageChange}
          isLoading={ui.loading?.users}
          className="admin-users-pagination"
        />

        {/* Modals */}
        {ui.showUserDetailModal && <UserDetailModal />}

        {/* Confirmation Modal */}
        <ConfirmationModal
          isOpen={confirmModal.isOpen}
          onClose={handleCancelAction}
          onConfirm={handleConfirmAction}
          title={
            confirmModal.type === "single" ? "Delete User" : "Delete Users"
          }
          message={
            confirmModal.type === "single" && confirmModal.user ? (
              <div>
                <p>
                  Are you sure you want to delete user{" "}
                  <strong>
                    "{confirmModal.user.firstName} {confirmModal.user.lastName}"
                  </strong>
                  ?
                </p>
                <p className="warning-text">
                  {/* This action will mark the user as deleted but preserve their data for integrity purposes. */}
                </p>
              </div>
            ) : confirmModal.type === "bulk" ? (
              <div>
                <p>
                  Are you sure you want to delete{" "}
                  <strong>{confirmModal.userIds.length} selected users</strong>?
                </p>
                <p className="warning-text">
                  This action will mark all selected users as deleted but
                  preserve their data for integrity purposes.
                </p>
              </div>
            ) : null
          }
          confirmText="Delete"
          cancelText="Cancel"
          type="danger"
          isLoading={isDeleting}
        />
      </div>
    </AdminLayout>
  );
};

export default AdminUserManagement;
