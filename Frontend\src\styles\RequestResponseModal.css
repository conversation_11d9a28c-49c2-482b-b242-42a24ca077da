/* Request Response Modal Styles */
.request-response-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.request-response-modal {
  background: var(--white);
  border-radius: var(--border-radius-large);
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--box-shadow-dark);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--heading5) var(--heading4);
  border-bottom: 1px solid var(--light-gray);
  background: var(--primary-light-color);
}

.modal-title {
  margin: 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--heading5);
  color: var(--gray);
  cursor: pointer;
  padding: var(--extrasmallfont);
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.modal-close:hover {
  background-color: var(--light-gray);
  color: var(--secondary-color);
}

.modal-content {
  padding: var(--heading4);
}

/* Request Summary */
.request-summary {
 border: 1px solid var(--light-gray);
  padding: var(--heading5);
  border-radius: var(--border-radius);
  margin-bottom: var(--heading4);
}

.request-summary h3 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--mediumfont);
  color: var(--secondary-color);
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--basefont);
  margin-bottom: var(--basefont);
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.summary-item label {
  font-size: var(--smallfont);
  color: var(--gray);
  font-weight: 500;
}

.summary-item span {
  font-size: var(--basefont);
  color: var(--secondary-color);
  font-weight: 500;
}

.request-description {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.request-description label {
  font-size: var(--smallfont);
  color: var(--gray);
  font-weight: 500;
}

.request-description p {
  margin: 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  line-height: 1.5;
  background: var(--white);
  padding: var(--extrasmallfont);
  border-radius: var(--border-radius);
}

/* Response Form */
.response-form {
  display: flex;
  flex-direction: column;
  gap: var(--heading5);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--border-radius-medium);
}

.form-label {
  display: flex;
  align-items: center;
  gap: var(--border-radius-medium);
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--secondary-color);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

/* Decision Buttons */
.decision-group {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--basefont);
  background: var(--white);
}

.decision-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--extrasmallfont);
}

.decision-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--border-radius-medium);
  padding: var(--extrasmallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--light-gray);
  color: var(--gray);
}

.decision-btn:hover {
  transform: translateY(-1px);
}

.accept-btn {
  border-color: var(--success-color);
  background-color: var(--second-primary-color);
  color: var(--white);
  border: none;
}



.accept-btn.active {
  background: var(--success-color);
  color: var(--white);
}

.reject-btn {

  background-color: var(--primary-color);
  color: var(--white);
}

.reject-btn:hover {
  background: var(--primary-color);
  color: var(--white);
}

.reject-btn.active {
  background: var(--error-color);
  color: var(--white);
}

/* Acceptance Details */
.acceptance-details {
  background: var(--success-light-color);
  padding: var(--basefont);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--success-color);
}

/* Form Inputs */
.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--extrasmallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-family: inherit;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--error-color);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-help {
  font-size: var(--smallfont);
  color: var(--gray);
  margin-top: var(--extrasmallfont);
}

.error-message {
  font-size: var(--smallfont);
  color: var(--error-color);
  margin-top: var(--extrasmallfont);
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--extrasmallfont);
  padding-top: var(--basefont);
  border-top: 1px solid var(--light-gray);
}

.btn-primary.accept {
  background: var(--primary-color);
  color: var(--white);
}

.btn-primary.accept:hover {
 transform: scale(1.02);
}

.btn-primary.reject {
  background: var(--error-color);
  border-color: var(--error-color);
}

.btn-primary.reject:hover {
  transform: scale(1.02);
}

/* Responsive Design */
@media (max-width: 768px) {
  .request-response-modal-overlay {
    padding: var(--extrasmallfont);
  }
  
  .modal-header {
    padding: var(--basefont);
  }
  
  .modal-content {
    padding: var(--basefont);
  }
  
  .modal-title {
    font-size: var(--heading6);
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .decision-buttons {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }
}
