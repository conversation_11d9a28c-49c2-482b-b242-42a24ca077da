const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const CustomRequest = require('../../models/CustomRequest');
const User = require('../../models/User');
const Content = require('../../models/Content');

// @desc    Get all custom requests with filtering, sorting, and pagination
// @route   GET /api/admin/requests
// @access  Private/Admin
exports.getAllRequests = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      contentType = '',
      sport = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom = '',
      dateTo = '',
      minBudget = '',
      maxBudget = '',
      buyerId = '',
      sellerId = ''
    } = req.query;

    // Build query
    let query = {};

    // Search functionality
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      const users = await User.find({
        $or: [
          { firstName: searchRegex },
          { lastName: searchRegex },
          { email: searchRegex }
        ]
      }).select('_id');

      const userIds = users.map(user => user._id);

      query.$or = [
        { buyer: { $in: userIds } },
        { seller: { $in: userIds } },
        { title: searchRegex },
        { description: searchRegex }
      ];
    }

    // Filter by status
    if (status) {
      query.status = status;
    }

    // Filter by content type
    if (contentType) {
      query.contentType = contentType;
    }

    // Filter by sport
    if (sport) {
      query.sport = sport;
    }

    // Filter by buyer
    if (buyerId) {
      query.buyer = buyerId;
    }

    // Filter by seller
    if (sellerId) {
      query.seller = sellerId;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) {
        query.createdAt.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        query.createdAt.$lte = new Date(dateTo);
      }
    }

    // Budget range filter
    if (minBudget || maxBudget) {
      query.budget = {};
      if (minBudget) {
        query.budget.$gte = parseFloat(minBudget);
      }
      if (maxBudget) {
        query.budget.$lte = parseFloat(maxBudget);
      }
    }

    // Sorting
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const requests = await CustomRequest.find(query)
      .populate('buyer', 'firstName lastName email profileImage')
      .populate('seller', 'firstName lastName email profileImage')
      .populate('relatedContent', 'title sport contentType')
      .sort(sortOptions)
      .limit(limit * 1)
      .skip((page - 1) * limit);

    // Get total count for pagination
    const total = await CustomRequest.countDocuments(query);

    res.status(200).json({
      success: true,
      data: requests,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get custom request by ID
// @route   GET /api/admin/requests/:id
// @access  Private/Admin
exports.getRequestById = async (req, res, next) => {
  try {
    const request = await CustomRequest.findById(req.params.id)
      .populate('buyer', 'firstName lastName email profileImage mobile phone')
      .populate('seller', 'firstName lastName email profileImage mobile phone')
      .populate('relatedContent', 'title description sport contentType files');

    if (!request) {
      return next(new ErrorResponse('Custom request not found', 404));
    }

    res.status(200).json({
      success: true,
      data: request
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update request status
// @route   PUT /api/admin/requests/:id/status
// @access  Private/Admin
exports.updateRequestStatus = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { status, notes } = req.body;

    const request = await CustomRequest.findById(req.params.id);
    if (!request) {
      return next(new ErrorResponse('Custom request not found', 404));
    }

    const oldStatus = request.status;
    request.status = status;

    // Add status change to request history if notes provided
    if (notes) {
      if (!request.statusHistory) {
        request.statusHistory = [];
      }
      request.statusHistory.push({
        status: status,
        notes: notes,
        changedBy: req.user.id,
        changedAt: new Date()
      });
    }

    await request.save();

    res.status(200).json({
      success: true,
      data: request,
      message: `Request status updated from ${oldStatus} to ${status}`
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get request statistics
// @route   GET /api/admin/requests/stats
// @access  Private/Admin
exports.getRequestStats = async (req, res, next) => {
  try {
    const totalRequests = await CustomRequest.countDocuments();
    const pendingRequests = await CustomRequest.countDocuments({ status: 'Pending' });
    const acceptedRequests = await CustomRequest.countDocuments({ status: 'Accepted' });
    const rejectedRequests = await CustomRequest.countDocuments({ status: 'Rejected' });
    const completedRequests = await CustomRequest.countDocuments({ status: 'Completed' });
    const cancelledRequests = await CustomRequest.countDocuments({ status: 'Cancelled' });

    // Budget stats
    const budgetStats = await CustomRequest.aggregate([
      {
        $group: {
          _id: null,
          totalBudget: { $sum: '$budget' },
          averageBudget: { $avg: '$budget' },
          highestBudget: { $max: '$budget' },
          lowestBudget: { $min: '$budget' }
        }
      }
    ]);

    // Content type distribution
    const contentTypeStats = await CustomRequest.aggregate([
      {
        $group: {
          _id: '$contentType',
          count: { $sum: 1 },
          totalBudget: { $sum: '$budget' },
          averageBudget: { $avg: '$budget' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Completion rate
    const completionRate = totalRequests > 0 ? (completedRequests / totalRequests) * 100 : 0;

    // Monthly stats for current year
    const currentYear = new Date().getFullYear();
    const monthlyStats = await CustomRequest.aggregate([
      {
        $match: {
          createdAt: {
            $gte: new Date(`${currentYear}-01-01`),
            $lte: new Date(`${currentYear}-12-31`)
          }
        }
      },
      {
        $group: {
          _id: { $month: '$createdAt' },
          count: { $sum: 1 },
          totalBudget: { $sum: '$budget' },
          completed: {
            $sum: { $cond: [{ $eq: ['$status', 'Completed'] }, 1, 0] }
          }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Average response time (time from creation to acceptance/rejection)
    const responseTimeStats = await CustomRequest.aggregate([
      {
        $match: {
          $or: [
            { 'sellerResponse.responseDate': { $exists: true } }
          ]
        }
      },
      {
        $project: {
          responseTime: {
            $subtract: ['$sellerResponse.responseDate', '$createdAt']
          }
        }
      },
      {
        $group: {
          _id: null,
          averageResponseTime: { $avg: '$responseTime' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        overview: {
          total: totalRequests,
          pending: pendingRequests,
          accepted: acceptedRequests,
          rejected: rejectedRequests,
          completed: completedRequests,
          cancelled: cancelledRequests,
          completionRate: completionRate.toFixed(2)
        },
        budgetStats: budgetStats[0] || {
          totalBudget: 0,
          averageBudget: 0,
          highestBudget: 0,
          lowestBudget: 0
        },
        contentTypeStats,
        monthlyStats,
        averageResponseTime: responseTimeStats[0]?.averageResponseTime || 0
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get request analytics
// @route   GET /api/admin/requests/analytics
// @access  Private/Admin
exports.getRequestAnalytics = async (req, res, next) => {
  try {
    const { period = '30d', groupBy = 'day' } = req.query;

    // Calculate date range based on period
    const endDate = new Date();
    let startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Build group stage based on groupBy parameter
    let groupStage = {};
    switch (groupBy) {
      case 'day':
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
        break;
      case 'week':
        groupStage = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        };
        break;
      case 'month':
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        };
        break;
      default:
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        };
    }

    const analytics = await CustomRequest.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: groupStage,
          totalRequests: { $sum: 1 },
          totalBudget: { $sum: '$budget' },
          averageBudget: { $avg: '$budget' },
          completed: {
            $sum: { $cond: [{ $eq: ['$status', 'Completed'] }, 1, 0] }
          },
          accepted: {
            $sum: { $cond: [{ $eq: ['$status', 'Accepted'] }, 1, 0] }
          },
          rejected: {
            $sum: { $cond: [{ $eq: ['$status', 'Rejected'] }, 1, 0] }
          }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export requests data
// @route   GET /api/admin/requests/export
// @access  Private/Admin
exports.exportRequests = async (req, res, next) => {
  try {
    const requests = await CustomRequest.find()
      .populate('buyer', 'firstName lastName email')
      .populate('seller', 'firstName lastName email')
      .populate('relatedContent', 'title sport contentType');

    // Transform data for export
    const exportData = requests.map(request => ({
      ID: request._id,
      'Title': request.title,
      'Description': request.description,
      'Content Type': request.contentType,
      'Budget': request.budget,
      'Status': request.status,
      'Buyer Name': request.buyer ? `${request.buyer.firstName} ${request.buyer.lastName}` : 'N/A',
      'Buyer Email': request.buyer ? request.buyer.email : 'N/A',
      'Seller Name': request.seller ? `${request.seller.firstName} ${request.seller.lastName}` : 'N/A',
      'Seller Email': request.seller ? request.seller.email : 'N/A',
      'Content Title': request.relatedContent ? request.relatedContent.title : 'N/A',
      'Sport': request.relatedContent ? request.relatedContent.sport : 'N/A',
      'Created At': request.createdAt,
      'Updated At': request.updatedAt
    }));

    res.status(200).json({
      success: true,
      data: exportData
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk update requests
// @route   POST /api/admin/requests/bulk-update
// @access  Private/Admin
exports.bulkUpdateRequests = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { requestIds, status } = req.body;

    const result = await CustomRequest.updateMany(
      { _id: { $in: requestIds } },
      {
        $set: {
          status,
          updatedAt: new Date()
        }
      }
    );

    res.status(200).json({
      success: true,
      message: `${result.modifiedCount} requests updated successfully`,
      data: result
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk delete requests
// @route   POST /api/admin/requests/bulk-delete
// @access  Private/Admin
exports.bulkDeleteRequests = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { requestIds } = req.body;

    const result = await CustomRequest.deleteMany({ _id: { $in: requestIds } });

    res.status(200).json({
      success: true,
      message: `${result.deletedCount} requests deleted successfully`,
      data: result
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete single request
// @route   DELETE /api/admin/requests/:id
// @access  Private/Admin
exports.deleteRequest = async (req, res, next) => {
  try {
    const request = await CustomRequest.findById(req.params.id);

    if (!request) {
      return next(new ErrorResponse('Custom request not found', 404));
    }

    await request.remove();

    res.status(200).json({
      success: true,
      message: 'Request deleted successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Flag a request
// @route   PUT /api/admin/requests/:id/flag
// @access  Private/Admin
exports.flagRequest = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { reason } = req.body;
    const request = await CustomRequest.findById(req.params.id);

    if (!request) {
      return next(new ErrorResponse('Custom request not found', 404));
    }

    request.isFlagged = true;
    request.flagReason = reason;
    request.flaggedBy = req.user.id;
    request.flaggedAt = new Date();

    await request.save();

    res.status(200).json({
      success: true,
      message: 'Request flagged successfully',
      data: request
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Unflag a request
// @route   PUT /api/admin/requests/:id/unflag
// @access  Private/Admin
exports.unflagRequest = async (req, res, next) => {
  try {
    const request = await CustomRequest.findById(req.params.id);

    if (!request) {
      return next(new ErrorResponse('Custom request not found', 404));
    }

    request.isFlagged = false;
    request.flagReason = undefined;
    request.flaggedBy = undefined;
    request.flaggedAt = undefined;

    await request.save();

    res.status(200).json({
      success: true,
      message: 'Request unflagged successfully',
      data: request
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Moderate a request
// @route   PUT /api/admin/requests/:id/moderate
// @access  Private/Admin
exports.moderateRequest = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { action, reason } = req.body;
    const request = await CustomRequest.findById(req.params.id);

    if (!request) {
      return next(new ErrorResponse('Custom request not found', 404));
    }

    switch (action) {
      case 'approve':
        request.moderationStatus = 'approved';
        request.moderatedBy = req.user.id;
        request.moderatedAt = new Date();
        break;
      case 'reject':
        if (!reason) {
          return next(new ErrorResponse('Reason is required for rejection', 400));
        }
        request.moderationStatus = 'rejected';
        request.moderationReason = reason;
        request.moderatedBy = req.user.id;
        request.moderatedAt = new Date();
        break;
      case 'flag':
        if (!reason) {
          return next(new ErrorResponse('Reason is required for flagging', 400));
        }
        request.isFlagged = true;
        request.flagReason = reason;
        request.flaggedBy = req.user.id;
        request.flaggedAt = new Date();
        break;
      default:
        return next(new ErrorResponse('Invalid moderation action', 400));
    }

    await request.save();

    res.status(200).json({
      success: true,
      message: `Request ${action}ed successfully`,
      data: request
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get request timeline
// @route   GET /api/admin/requests/:id/timeline
// @access  Private/Admin
exports.getRequestTimeline = async (req, res, next) => {
  try {
    const request = await CustomRequest.findById(req.params.id)
      .populate('buyer', 'firstName lastName')
      .populate('seller', 'firstName lastName')
      .populate('moderatedBy', 'firstName lastName')
      .populate('flaggedBy', 'firstName lastName');

    if (!request) {
      return next(new ErrorResponse('Custom request not found', 404));
    }

    // Build timeline events
    const timeline = [
      {
        type: 'created',
        date: request.createdAt,
        details: `Request created by ${request.buyer.firstName} ${request.buyer.lastName}`
      }
    ];

    // Add status history events
    if (request.statusHistory && request.statusHistory.length > 0) {
      const statusEvents = request.statusHistory.map(history => ({
        type: 'status_change',
        date: history.changedAt,
        details: `Status changed to ${history.status}${history.notes ? `: ${history.notes}` : ''}`
      }));
      timeline.push(...statusEvents);
    }

    // Add moderation events
    if (request.moderatedAt) {
      timeline.push({
        type: 'moderation',
        date: request.moderatedAt,
        details: `Request ${request.moderationStatus} by ${request.moderatedBy.firstName} ${request.moderatedBy.lastName}${request.moderationReason ? `: ${request.moderationReason}` : ''
          }`
      });
    }

    // Add flag events
    if (request.flaggedAt) {
      timeline.push({
        type: 'flag',
        date: request.flaggedAt,
        details: `Request flagged by ${request.flaggedBy.firstName} ${request.flaggedBy.lastName}: ${request.flagReason}`
      });
    }

    // Sort timeline by date
    timeline.sort((a, b) => b.date - a.date);

    res.status(200).json({
      success: true,
      data: timeline
    });
  } catch (err) {
    next(err);
  }
};
