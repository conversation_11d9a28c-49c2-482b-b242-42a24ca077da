import axios from 'axios';
import { STORAGE_KEYS } from '../../utils/constants';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: `${API_URL}/admin/requests`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Get all custom requests with filtering, sorting, and pagination
export const getAllRequests = async (params = {}) => {
  try {
    const response = await api.get('/', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get request by ID
export const getRequestById = async (id) => {
  try {
    const response = await api.get(`/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Note: Edit/Delete operations removed - Custom requests are read-only for admin monitoring

// Get request statistics
export const getRequestStats = async () => {
  try {
    const response = await api.get('/stats');
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get request analytics
export const getRequestAnalytics = async (params = {}) => {
  try {
    const response = await api.get('/analytics', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Get request timeline
export const getRequestTimeline = async (id) => {
  try {
    const response = await api.get(`/${id}/timeline`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

// Export requests
export const exportRequests = async (params = {}) => {
  try {
    const response = await api.get('/export', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error.message;
  }
};

export default {
  getAllRequests,
  getRequestById,
  getRequestStats,
  getRequestAnalytics,
  getRequestTimeline,
  exportRequests
};
