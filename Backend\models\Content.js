const mongoose = require("mongoose");

const ContentSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Please add a title"],
      trim: true,
      maxlength: [100, "Title cannot be more than 100 characters"],
    },
    description: {
      type: String,
      required: [true, "Please add a description"],
      maxlength: [50000, "Description cannot be more than 50000 characters"],
      // REMOVED: Word count validation to allow unlimited text
    },
    sport: {
      type: String,
      required: [true, "Please specify the sport"],
      enum: [
        "Basketball",
        "Football",
        "Soccer",
        "Baseball",
        "Tennis",
        "Golf",
        "Swimming",
        "Volleyball",
        "Running",
        "Cycling",
        "Fitness",
        "Yoga",
        "Other",
      ],
    },
    contentType: {
      type: String,
      required: [true, "Please specify the content type"],
      enum: ["Video", "Document", "Audio", "Image", "Text"],
    },
    fileUrl: {
      type: String,
      required: [true, "Please upload a file"],
    },
    fileUrlGeneratedAt: {
      type: Date,
      index: true,
    },
    previewUrl: {
      type: String,
      index: true,
    },
    previewUrlGeneratedAt: {
      type: Date,
      index: true,
    },
    previewStatus: {
      type: String,
      enum: ["pending", "processing", "completed", "failed", "not_supported"],
      default: "pending",
      index: true,
    },
    previewError: {
      type: String,
    },
    thumbnailUrl: {
      type: String,
    },
    duration: {
      type: Number,
      min: [0, "Duration cannot be negative"],
    },
    fileSize: {
      type: Number,
      min: [0, "File size cannot be negative"],
    },
    tags: [String],
    category: {
      type: String,
      required: [true, "Please specify the category"],
      trim: true,
      maxlength: [50, "Category cannot be more than 50 characters"],
    },
    difficulty: {
      type: String,
      enum: ["Beginner", "Intermediate", "Advanced", "Professional"],
      required: [true, "Please specify the difficulty level"],
    },
    coachName: {
      type: String,
      required: [true, "Please add coach/seller/academy name"],
      trim: true,
      maxlength: [100, "Coach name cannot be more than 100 characters"],
    },
    aboutCoach: {
      type: String,
      required: [true, "Please add information about the coach"],
      maxlength: [
        50000,
        "About coach section cannot be more than 50000 characters",
      ],
    },
    strategicContent: {
      type: String,
      required: [true, "Please describe what strategic content is included"],
      maxlength: [
        50000,
        "Strategic content description cannot be more than 50000 characters",
      ],
    },
    videoLength: {
      type: Number,
      min: [0, "Video length cannot be negative"],
    },
    language: {
      type: String,
      enum: [
        "English",
        "Spanish",
        "French",
        "German",
        "Italian",
        "Portuguese",
        "Chinese",
        "Japanese",
        "Korean",
        "Other",
      ],
      default: "English",
    },
    prerequisites: [
      {
        type: String,
        trim: true,
        maxlength: [
          100,
          "Each prerequisite cannot be more than 100 characters",
        ],
      },
    ],
    learningObjectives: [
      {
        type: String,
        trim: true,
        maxlength: [
          200,
          "Each learning objective cannot be more than 200 characters",
        ],
      },
    ],
    equipment: [
      {
        type: String,
        trim: true,
        maxlength: [
          100,
          "Each equipment item cannot be more than 100 characters",
        ],
      },
    ],
    saleType: {
      type: String,
      enum: ["Fixed", "Auction", "Both"],
      default: "Fixed",
    },
    price: {
      type: Number,
      min: [0, "Price cannot be negative"],
    },
    auctionDetails: {
      basePrice: {
        type: Number,
        min: [0, "Base price cannot be negative"],
      },
      startingBid: {
        type: Number,
        min: [0, "Starting bid cannot be negative"],
      },
      minIncrement: {
        type: Number,
        min: [0, "Minimum increment cannot be negative"],
      },
      minimumBidIncrement: {
        type: Number,
        min: [0, "Minimum bid increment cannot be negative"],
      },
      reservePrice: {
        type: Number,
        min: [0, "Reserve price cannot be negative"],
      },
      auctionStartDate: {
        type: Date,
        validate: {
          validator: function (value) {
            if (!value) return true; // Allow empty values

            // Get the document context - works for both document and query operations
            const doc = this.parent ? this.parent() : this;

            // Allow past dates if auction is already ended (bid accepted scenario)
            if (doc && doc.auctionStatus === 'Ended') {
              return true;
            }

            // Allow past dates if this is an existing document (not new)
            if (doc && !doc.isNew) {
              return true;
            }

            // For new auctions, start date must be in the future
            // Convert both dates to UTC for comparison
            const now = new Date();
            const startDate = new Date(value);
            return startDate.getTime() > now.getTime();
          },
          message: 'Auction start date must be in the future'
        }
      },
      auctionEndDate: {
        type: Date,
        validate: {
          validator: function (value) {
            if (!value) return true; // Allow empty values

            // Get the document context - works for both document and query operations
            const doc = this.parent ? this.parent() : this;
            const startDate = doc && doc.auctionDetails ? doc.auctionDetails.auctionStartDate : null;

            if (!startDate) return true; // Allow if no start date

            // Allow any end date if auction is already ended (bid accepted scenario)
            if (doc && doc.auctionStatus === 'Ended') {
              return true;
            }

            // Convert both dates to UTC timestamps for comparison
            const endTime = new Date(value).getTime();
            const startTime = new Date(startDate).getTime();
            return endTime > startTime;
          },
          message: 'Auction end date must be after start date'
        }
      },
      endTime: {
        type: Date,
      },
      allowOfferBeforeAuctionStart: {
        type: Boolean,
        default: false,
      },
    },
    allowCustomRequests: {
      type: Boolean,
      default: false,
    },
    customRequestPrice: {
      type: Number,
      min: [0, "Custom request price cannot be negative"],
    },
    status: {
      type: String,
      enum: ["Draft", "Published", "Archived"],
      default: "Draft",
    },
    visibility: {
      type: String,
      enum: ["Public", "Private"],
      default: "Public",
    },
    isActive: {
      type: Number,
      enum: [-1, 0, 1], // -1 = deleted, 0 = inactive, 1 = active
      default: 1,
      index: true,
    },
    seller: {
      type: mongoose.Schema.ObjectId,
      ref: "User",
      required: true,
    },
    averageRating: {
      type: Number,
      min: [0, "Rating cannot be negative"],
      max: [5, "Rating must not be more than 5"],
    },
    isCustomContent: {
      type: Boolean,
      default: false,
    },
    customRequestId: {
      type: mongoose.Schema.ObjectId,
      ref: "CustomRequest",
    },
    publishedDate: {
      type: Date,
    },
    lastUpdated: {
      type: Date,
      default: Date.now,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    // Auction and sale status fields
    isSold: {
      type: Boolean,
      default: false,
      index: true,
    },
    soldAt: {
      type: Date,
    },
    auctionStatus: {
      type: String,
      enum: ["Active", "Ended", "Cancelled"],
      default: "Active",
      index: true,
    },
    auctionEndedAt: {
      type: Date,
    },
    winningBidId: {
      type: mongoose.Schema.ObjectId,
      ref: "Bid",
    },
    winningOfferId: {
      type: mongoose.Schema.ObjectId,
      ref: "Offer",
    },
    statusHistory: [{
      status: {
        type: String,
        enum: ['Draft', 'Published', 'Under Review', 'Rejected', 'Archived']
      },
      changedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      changedAt: {
        type: Date,
        default: Date.now
      },
      reason: String
    }],
    uploadDate: {
      type: Date,
      default: Date.now
    }
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtual for reviews
ContentSchema.virtual("reviews", {
  ref: "Review",
  localField: "_id",
  foreignField: "content",
  justOne: false,
});

// Virtual for bids
ContentSchema.virtual("bids", {
  ref: "Bid",
  localField: "_id",
  foreignField: "content",
  justOne: false,
});

// Pre-save middleware to update lastUpdated and set publishedDate
ContentSchema.pre("save", function (next) {
  // Update lastUpdated on every save
  this.lastUpdated = new Date();

  // Set publishedDate when status changes to Published for the first time
  if (
    this.isModified("status") &&
    this.status === "Published" &&
    !this.publishedDate
  ) {
    this.publishedDate = new Date();
  }

  // Pre-save middleware to track status changes
  if (this.isModified('status')) {
    this.statusHistory.push({
      status: this.status,
      changedBy: this.updatedBy || this.createdBy,
      changedAt: new Date(),
    });
  }

  next();
});

// Pre-update middleware for findOneAndUpdate operations
ContentSchema.pre(
  ["findOneAndUpdate", "updateOne", "updateMany"],
  function (next) {
    this.set({ lastUpdated: new Date() });

    // Set publishedDate when status changes to Published
    if (this.getUpdate().$set && this.getUpdate().$set.status === "Published") {
      this.set({ publishedDate: new Date() });
    }

    next();
  }
);

module.exports = mongoose.model("Content", ContentSchema);
