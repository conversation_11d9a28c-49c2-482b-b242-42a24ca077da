import React, { useState } from "react";
import { FaTimes } from "react-icons/fa";
import { useSelector } from "react-redux";
import useModalScrollLock from "../../hooks/useModalScrollLock";
import "../../styles/RequestCustomTrainingModal.css";
import Thankyou from "../../assets/images/thankyou.svg";
import api from "../../services/api";
import { toast } from "react-toastify";

const RequestCustomTrainingModal = ({ isOpen, onClose, strategy }) => {
  // Use modal scroll lock hook
  useModalScrollLock(isOpen);

  const { user } = useSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    contentType: "Video",
    requestedDeliveryDate: "",
    budget: ""
  });
  const [showSuccessState, setShowSuccessState] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    }

    if (!formData.budget || parseFloat(formData.budget) <= 0) {
      newErrors.budget = "Budget must be a positive number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Handle both populated seller object and seller ID string
      const sellerId = typeof strategy.seller === 'object' && strategy.seller._id
        ? strategy.seller._id
        : strategy.seller;

      const requestData = {
        sellerId: sellerId,
        relatedContentId: strategy._id, // Reference content for inspiration only - NOT for purchase
        title: formData.title,
        description: formData.description,
        sport: strategy.sport,
        contentType: formData.contentType,
        requestedDeliveryDate: formData.requestedDeliveryDate || undefined,
        budget: parseFloat(formData.budget)
      };

      await api.post('/requests', requestData);

      setShowSuccessState(true);
      toast.success("Custom request submitted successfully!");
    } catch (error) {
      console.error('Error submitting custom request:', error);
      toast.error(error.response?.data?.message || "Failed to submit custom request");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCloseModal = () => {
    // Reset form and success state
    setFormData({
      title: "",
      description: "",
      contentType: "Video",
      requestedDeliveryDate: "",
      budget: ""
    });
    setShowSuccessState(false);
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      handleCloseModal();
    }
  };

  if (!isOpen || !strategy) return null;

  return (
    <div className="request-modal-overlay" onClick={handleOverlayClick}>
      <div className={`request-modal ${showSuccessState ? 'request-modal--success' : ''}`}>
        <div className="request-modal__header">
          <h2 className="request-modal__title">
            {showSuccessState ? "Request Submitted Successfully!" : "Request Custom Content"}
          </h2>
          <button className="request-modal__close" onClick={handleCloseModal}>
            <FaTimes />
          </button>
        </div>

        <div className="request-modal__content">
          {showSuccessState ? (
            <div className="request-modal__success">
              <div className="success-icon">
                <img src={Thankyou} alt="thankyou" />
              </div>
              <p className="success-message">
                Your custom request has been submitted successfully!
                The seller will review your request and respond soon.
              </p>
              <p className="success-submessage">
                You can track the status of your request in your dashboard.
              </p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="request-modal__form">
              <div className="request-modal__strategy-info">
                <h4>Requesting NEW custom content inspired by:</h4>
                <p className="strategy-title">{strategy.title}</p>
                <p className="strategy-coach">by {strategy.coachName}</p>
                <div className="custom-request-notice">
                  <p><strong>Note:</strong> This is a request for NEW content creation, not access to the existing content above. The seller will create custom content based on your requirements.</p>
                </div>
              </div>

              <div className="request-modal__form-group">
                <label htmlFor="title">Request Title *</label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className={`request-modal__input ${errors.title ? 'error' : ''}`}
                  placeholder="e.g., Custom Basketball Training Video"
                  required
                />
                {errors.title && <span className="error-message">{errors.title}</span>}
              </div>

              <div className="request-modal__form-group">
                <label htmlFor="description">Description *</label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className={`request-modal__textarea ${errors.description ? 'error' : ''}`}
                  placeholder="Describe what you need in detail..."
                  rows="4"
                  required
                />
                {errors.description && <span className="error-message">{errors.description}</span>}
              </div>

              <div className="request-modal__form-row">
                <div className="request-modal__form-group">
                  <label htmlFor="contentType">Content Type</label>
                  <select
                    id="contentType"
                    name="contentType"
                    value={formData.contentType}
                    onChange={handleInputChange}
                    className="request-modal__select"
                  >
                    <option value="Video">Video</option>
                    <option value="Document">Document</option>

                  </select>
                </div>

                <div className="request-modal__form-group">
                  <label htmlFor="budget">Budget (USD) *</label>
                  <input
                    type="number"
                    id="budget"
                    name="budget"
                    value={formData.budget}
                    onChange={handleInputChange}
                    className={`request-modal__input ${errors.budget ? 'error' : ''}`}
                    placeholder="0.00"
                    min="0.01"
                    step="0.01"
                    required
                  />
                  {errors.budget && <span className="error-message">{errors.budget}</span>}
                </div>
              </div>

              <div className="request-modal__form-group">
                <label htmlFor="requestedDeliveryDate">Requested Delivery Date (Optional)</label>
                <input
                  type="date"
                  id="requestedDeliveryDate"
                  name="requestedDeliveryDate"
                  value={formData.requestedDeliveryDate}
                  onChange={handleInputChange}
                  className="request-modal__input"
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              <button
                type="submit"
                className="btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Send Request"}
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default RequestCustomTrainingModal;
